import os
import argparse
import subprocess
import sys
import pandas as pd
import time
from pathlib import Path
from typing import Dict, Optional, Tuple

# WICHTIG: Dieses Skript steuert die Ausführung der vorhandenen VBS-Skripte.
# Es setzt zur Laufzeit die in beschreibung.md geforderten Variablen in temporären
# Kopien der VBS-Dateien ein, führt sie in der gewünschten Reihenfolge aus,
# schließt Excel und liest die erforderlichen Werte aus der exportierten Datei.
#
# Es nutzt ausschließlich Standard-Bibliotheken und Windows-Bordmittel
# (cscript.exe, taskkill). Für SAP/Excel-Statusprüfungen sind optionale
# pywin32-Aufrufe möglich, werden hier aber bewusst vermieden, um Abhängigkeiten
# klein zu halten. Stattdessen wird SAP bei Bedarf via sapshcut.exe gestartet
# und wir warten kurz, bis Scripting verfügbar ist.


SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "TS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"

# Standardwerte aus beschreibung.md (werden ggf. überschrieben)
LAGERORT_VON = "1090"
LAGERORT_NACH = "1010"
LAGERTYP = "957"
LAGERNUMMER_VON = "51B"
LAGERNUMMER_NACH = "512"
WERK = "5100"
EXPORT_FILE_NAME = "lsgitls24"
EXPORT_FILE_PATH = r"C:\\TEMP"


def print_progress(step: int, total: int, message: str) -> None:
    """Gibt einen Fortschritts-Marker aus, den die UI auswerten kann."""
    percent = int(step * 100 / max(1, total))
    print(f"PROGRESS {percent} {message}")
    sys.stdout.flush()

def log_message(message: str) -> None:
    """Gibt eine normale Log-Nachricht aus, die in der GUI angezeigt wird."""
    print(message)
    sys.stdout.flush()


def start_sap_if_needed() -> None:
    """Startet SAP via SSO, falls noch keine Session offen ist.

    Vereinfachung: Wir versuchen immer einen Start, wenn kein SAP-Scripting
    erreichbar erscheint. Da wir ohne pywin32 arbeiten, prüfen wir pragmatisch,
    ob der sapshcut-Aufruf erfolgreich ist und warten dann kurz.
    """
    try:
        # Versuche, SAP per SSO zu starten. Wenn bereits offen, wird ggf.
        # eine weitere Verbindung hergestellt, was laut Anforderung ok ist.
        args = [
            SAP_EXECUTABLE_PATH,
            f"-system={SAP_SYSTEM_ID}",
            f"-client={SAP_CLIENT}",
            f"-language={SAP_LANGUAGE}",
            "-reuse=1",
            "-maxgui"
        ]
        subprocess.Popen(args)
        # Kurzes Warten, damit SAP starten kann
        log_message("Warte 5s, damit SAP GUI starten kann...")
        time.sleep(5)
    except FileNotFoundError:
        # Wenn sapshcut.exe fehlt, fahren wir fort – ggf. ist SAP bereits offen
        log_message("WARNUNG: sapshcut.exe nicht gefunden. Es wird angenommen, dass SAP bereits läuft.")
        pass


def create_temp_vbs(original: Path, replacements: Dict[str, str]) -> Path:
    """Erstellt eine temporäre VBS-Kopie mit einfachen String-Replacements.

    replacements: Mapping von exaktem Suchstring -> Ersatzstring.
    """
    content = original.read_text(encoding="utf-8", errors="ignore")
    for search, replace in replacements.items():
        content = content.replace(search, replace)

    temp_path = original.with_name(f"__temp__{original.name}")
    temp_path.write_text(content, encoding="utf-8")
    return temp_path


def run_vbs(vbs_path: Path, timeout: int = 300) -> None:
    """Führt ein VBS-Skript synchron mit cscript aus."""
    cmd = ["cscript.exe", "//nologo", str(vbs_path)]
    proc = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
    if proc.returncode != 0:
        raise RuntimeError(f"VBS-Fehler ({vbs_path.name}): {proc.stderr or proc.stdout}")


def close_excel() -> None:
    """Schließt alle laufenden Excel-Prozesse robust mit taskkill."""
    try:
        subprocess.run(["taskkill", "/F", "/IM", "excel.exe"], check=True, capture_output=True, text=True)
    except subprocess.CalledProcessError as e:
        # Excel war evtl. nicht offen – das ist ok
        _ = e


def find_export_file(folder: Path, base_name: str) -> Optional[Path]:
    """Sucht nach der exportierten Datei: bevorzugt .xlsx, sonst .xls."""
    xlsx = folder / f"{base_name}.xlsx"
    xls = folder / f"{base_name}.xls"
    if xlsx.exists():
        return xlsx
    if xls.exists():
        return xls
    # Fallback: irgendeine Datei, die so beginnt
    candidates = sorted(folder.glob(f"{base_name}.*"), key=lambda p: p.stat().st_mtime, reverse=True)
    return candidates[0] if candidates else None


def read_cells_from_excel(file_path: Path) -> Tuple[str, str, str]:
    """Liest U2, C2, AG2 aus Excel. Nutzt COM über 'excel.exe', da Export oft .xls ist.

    Rückgabe: (TRANSPORTAUFTRAG_NR, MATERIAL_NR, GESAMTBESTAND)
    """
    try:
        import win32com.client  # type: ignore
    except Exception as e:
        raise RuntimeError("pywin32 (win32com) wird benötigt, um Excel-Dateien auszulesen.") from e

    excel = None
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        wb = excel.Workbooks.Open(str(file_path))
        ws = wb.ActiveSheet
        # Werte als Strings lesen (None -> "")
        val_u2 = ws.Range("U2").Value
        val_c2 = ws.Range("C2").Value
        val_ag2 = ws.Range("AG2").Value
        wb.Close(SaveChanges=False)
        return str(val_u2 or "").strip(), str(val_c2 or "").strip(), str(val_ag2 or "").strip()
    finally:
        if excel is not None:
            excel.Quit()


def _resource_path(relative: str) -> Path:
    """Liefert einen Ressourcenpfad, kompatibel zu PyInstaller (MEIPASS) und dev.

    - Im Freeze (portable .exe): basiert auf dem entpackten Temp-Verzeichnis.
    - Im Dev-Modus: basiert auf dem Ordner der Datei.
    """
    base = Path(getattr(sys, "_MEIPASS", Path(__file__).parent))
    return base / relative


def main() -> None:
    parser = argparse.ArgumentParser(description="Orchestriert SAP-VBS-Skripte basierend auf beschreibung.md")
    parser.add_argument("--charge", required=True, help="Chargennummer (CHARGEN_NR)")
    parser.add_argument("--dry-run", action="store_true", help="Trockenlauf ohne SAP/VBS – simuliert alle Schritte")
    args = parser.parse_args()

    log_message(f"Orchestrator gestartet mit Charge: {args.charge}")
    base_dir = _resource_path("SAPScripts")
    if not base_dir.exists():
        raise FileNotFoundError("SAPScripts-Verzeichnis nicht gefunden")

    if args.dry_run:
        # Trockenlauf: keine echte SAP-Interaktion; wir simulieren Variablen
        print_progress(1, 10, "Trockenlauf aktiv – keine SAP-Interaktion")
        # Simulierte Daten
        transportauftrag_nr = "1030"
        material_nr = "281603"
        gesamtbestand = "305"
        time.sleep(0.2)
        print_progress(2, 10, "Simuliere Script 1 (llsgitls24.vbs)")
        time.sleep(0.2)
        print_progress(4, 10, "Simuliere Script 2 (lt15.vbs)")
        time.sleep(0.2)
        print_progress(5, 10, "Simuliere Script 3 (MIGO.vbs)")
        time.sleep(0.2)
        print_progress(6, 10, "Simuliere Script 4 (lt0651b.vbs)")
        time.sleep(0.2)
        print_progress(7, 10, "Simuliere Script 5 (lt12.vbs)")
        time.sleep(0.2)
        print_progress(8, 10, "Simuliere Script 6 (lt06512.vbs)")
        time.sleep(0.2)
        print_progress(9, 10, "Trockenlauf-Aufräumen")
        print_progress(10, 10, "Fertig")
        return

    # 1) SAP sicherstellen
    print_progress(1, 10, "Starte/prüfe SAP")
    log_message("Prüfe, ob SAP gestartet werden muss...")
    start_sap_if_needed()

    # 2) Script 1: llsgitls24.vbs -> Variablen ersetzen, ausführen, Excel schließen, Zellen lesen
    print_progress(2, 10, "Führe Script 1 (llsgitls24.vbs) aus")
    log_message("Bereite Script 1 vor: llsgitls24.vbs")
    vbs1 = base_dir / "llsgitls24.vbs"
    repl1 = {
        'txtS_CHARG-LOW").text = "6356375708"': f'txtS_CHARG-LOW").text = "{args.charge}"',
        'txtGS_EXPORT-FILE_NAME").text = "lsgitls24"': f'txtGS_EXPORT-FILE_NAME").text = "{EXPORT_FILE_NAME}"',
        'ctxtDY_PATH").text = "C:\\TEMP"': f'ctxtDY_PATH").text = "{EXPORT_FILE_PATH}"',
        'ctxtS_LGNUM-LOW").text = "51b"': f'ctxtS_LGNUM-LOW").text = "{LAGERNUMMER_VON}"',
        'ctxtS_WERKS-LOW").text = "5100"': f'ctxtS_WERKS-LOW").text = "{WERK}"',
        'ctxtS_LGORT-LOW").text = "1090"': f'ctxtS_LGORT-LOW").text = "{LAGERORT_VON}"',
    }
    vbs1_tmp = create_temp_vbs(vbs1, repl1)
    try:
        log_message(f"Führe temporäres Skript aus: {vbs1_tmp.name}")
        run_vbs(vbs1_tmp, timeout=300)
    finally:
        try:
            vbs1_tmp.unlink(missing_ok=True)
        except Exception:
            pass

    # Excel schließen, das SAP evtl. geöffnet hat
    log_message("Schließe Excel, um den Export zu ermöglichen...")
    close_excel()

    # Export-Datei finden und Zellen lesen
    print_progress(3, 10, "Lese Export-Excel")
    log_message(f"Suche nach Export-Datei '{EXPORT_FILE_NAME}' in '{EXPORT_FILE_PATH}'...")
    export_path = Path(EXPORT_FILE_PATH)
    export_file = find_export_file(export_path, EXPORT_FILE_NAME)
    if not export_file:
        raise FileNotFoundError(f"Export-Datei nicht gefunden in {EXPORT_FILE_PATH} mit Basis '{EXPORT_FILE_NAME}'")
    log_message(f"Export-Datei gefunden: {export_file}")
    transportauftrag_nr, material_nr, gesamtbestand = read_cells_from_excel(export_file)
    log_message(f"Gelesene Werte: TA-Nr={transportauftrag_nr}, Material={material_nr}, Bestand={gesamtbestand}")
    if not transportauftrag_nr:
        raise RuntimeError("TRANSPORTAUFTRAG_NR aus U2 ist leer.")

    # 3) Script 2: lt15.vbs (Transportauftrag setzen)
    print_progress(4, 10, "Führe Script 2 (lt15.vbs) aus")
    log_message("Bereite Script 2 vor: lt15.vbs")
    vbs2 = base_dir / "lt15.vbs"
    repl2 = {
        'txtLTAK-TANUM").text = "1030"': f'txtLTAK-TANUM").text = "{transportauftrag_nr}"',
    }
    vbs2_tmp = create_temp_vbs(vbs2, repl2)
    try:
        run_vbs(vbs2_tmp, timeout=180)
        log_message("Script 2 (lt15.vbs) ausgeführt.")
    finally:
        try:
            vbs2_tmp.unlink(missing_ok=True)
        except Exception:
            pass

    # 4) Script 3: MIGO.vbs (Material, Bestand, Lagerorte, Werk, Charge)
    print_progress(5, 10, "Führe Script 3 (MIGO.vbs) aus")
    log_message("Bereite Script 3 vor: MIGO.vbs")
    vbs3 = base_dir / "MIGO.vbs"
    repl3 = {
        'ctxtGOITEM-MAKTX[1,0]").text = "281603"': f'ctxtGOITEM-MAKTX[1,0]").text = "{material_nr}"',
        'txtGOITEM-ERFMG[4,0]").text = "305"': f'txtGOITEM-ERFMG[4,0]").text = "{gesamtbestand}"',
        'ctxtGOITEM-LGOBE[6,0]").text = "1090"': f'ctxtGOITEM-LGOBE[6,0]").text = "{LAGERORT_VON}"',
        'ctxtGOITEM-NAME1[13,0]").text = "5100"': f'ctxtGOITEM-NAME1[13,0]").text = "{WERK}"',
        'ctxtGOITEM-CHARG[10,0]").text = "6356360869"': f'ctxtGOITEM-CHARG[10,0]").text = "{args.charge}"',
        'ctxtGOITEM-UMLGOBE[32,0]").text = "1010"': f'ctxtGOITEM-UMLGOBE[32,0]").text = "{LAGERORT_NACH}"',
        'ctxtGOITEM-UMCHA[36,0]").text = "6356360869"': f'ctxtGOITEM-UMCHA[36,0]").text = "{args.charge}"',
    }
    vbs3_tmp = create_temp_vbs(vbs3, repl3)
    try:
        run_vbs(vbs3_tmp, timeout=240)
        log_message("Script 3 (MIGO.vbs) ausgeführt.")
    finally:
        try:
            vbs3_tmp.unlink(missing_ok=True)
        except Exception:
            pass

    # 5) Script 4: lt0651b.vbs (Lagertyp setzen)
    print_progress(6, 10, "Führe Script 4 (lt0651b.vbs) aus")
    log_message("Bereite Script 4 vor: lt0651b.vbs")
    vbs4 = base_dir / "lt0651b.vbs"
    repl4 = {
        'ctxtLTAP-VLTYP").text = "957"': f'ctxtLTAP-VLTYP").text = "{LAGERTYP}"',
    }
    vbs4_tmp = create_temp_vbs(vbs4, repl4)
    try:
        run_vbs(vbs4_tmp, timeout=180)
        log_message("Script 4 (lt0651b.vbs) ausgeführt.")
    finally:
        try:
            vbs4_tmp.unlink(missing_ok=True)
        except Exception:
            pass

    # 6) Script 5: lt12.vbs (lt12b.vbs existiert nicht, wir nutzen lt12.vbs)
    print_progress(7, 10, "Führe Script 5 (lt12.vbs) aus")
    log_message("Führe Script 5 aus: lt12.vbs")
    vbs5 = base_dir / "lt12.vbs"
    run_vbs(vbs5, timeout=120)

    # 7) Script 6: lt06512.vbs (Lagernummer nach setzen)
    print_progress(8, 10, "Führe Script 6 (lt06512.vbs) aus")
    vbs6 = base_dir / "lt06512.vbs"
    repl6 = {
        'ctxtRL02B-LGNUM").text = "512"': f'ctxtRL02B-LGNUM").text = "{LAGERNUMMER_NACH}"',
    }
    vbs6_tmp = create_temp_vbs(vbs6, repl6)
    try:
        run_vbs(vbs6_tmp, timeout=240)
    finally:
        try:
            vbs6_tmp.unlink(missing_ok=True)
        except Exception:
            pass

    # 8) Aufräumen: Excel sicher schließen (falls wieder geöffnet) und optional SAP beenden
    print_progress(9, 10, "Räume auf (Excel/SAP)")
    close_excel()
    # SAP GUI schließen (Soft-Ansatz: Prozess killen, da scripting-close uneinheitlich)
    try:
        subprocess.run(["taskkill", "/F", "/IM", "saplogon.exe"], check=False, capture_output=True, text=True)
    except Exception:
        pass

    print_progress(10, 10, "Fertig")


if __name__ == "__main__":
    main()
