import argparse
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional, Tuple

# WICHTIG: <PERSON>ses <PERSON> implementiert SAP GUI Automation direkt in Python.
# Es verbindet sich mit SAP GUI über COM automation und führt die gleichen
# Aktionen aus, die in den VBS-Referenzdateien beschrieben sind.
# Die VBS-Dateien dienen nur als Referenz für die SAP GUI Interaktionen.
#
# Das Skript nutzt pywin32 für COM automation mit SAP GUI und Excel.
# Es startet SAP bei Bedarf via sapshcut.exe und führt alle Transaktionen
# direkt über Python aus, anstatt externe VBS-Skripte zu verwenden.


SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "TS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"

# Standardwerte aus beschreibung.md (werden ggf. überschrieben)
LAGERORT_VON = "1090"
LAGERORT_NACH = "1010"
LAGERTYP = "957"
LAGERNUMMER_VON = "51B"
LAGERNUMMER_NACH = "512"
WERK = "5100"
EXPORT_FILE_NAME = "lsgitls24"
EXPORT_FILE_PATH = r"C:\\TEMP"


def print_progress(step: int, total: int, message: str) -> None:
    """Gibt einen Fortschritts-Marker aus, den die UI auswerten kann."""
    percent = int(step * 100 / max(1, total))
    print(f"PROGRESS {percent} {message}")
    sys.stdout.flush()

def log_message(message: str) -> None:
    """Gibt eine normale Log-Nachricht aus, die in der GUI angezeigt wird."""
    print(message)
    sys.stdout.flush()


def get_sap_session():
    """Verbindet sich mit SAP GUI und gibt die Session zurück."""
    try:
        import win32com.client  # type: ignore
    except ImportError:
        raise RuntimeError("pywin32 (win32com) wird benötigt für SAP GUI automation.")

    try:
        # Versuche, eine bestehende SAP GUI Session zu finden
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        if application.Children.Count > 0:
            connection = application.Children(0)
            if connection.Children.Count > 0:
                session = connection.Children(0)
                log_message("Bestehende SAP Session gefunden.")
                return session
    except Exception:
        log_message("Keine bestehende SAP Session gefunden.")

    # Starte SAP neu
    start_sap_if_needed()

    # Versuche erneut, eine Session zu bekommen
    try:
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        if application.Children.Count > 0:
            connection = application.Children(0)
            if connection.Children.Count > 0:
                session = connection.Children(0)
                log_message("SAP Session erfolgreich verbunden.")
                return session
    except Exception as e:
        raise RuntimeError(f"Konnte keine SAP Session erstellen: {e}")

    raise RuntimeError("SAP GUI Session konnte nicht erstellt werden.")


def start_sap_if_needed() -> None:
    """Startet SAP via SSO, falls noch keine Session offen ist."""
    try:
        args = [
            SAP_EXECUTABLE_PATH,
            f"-system={SAP_SYSTEM_ID}",
            f"-client={SAP_CLIENT}",
            f"-language={SAP_LANGUAGE}",
            "-reuse=1",
            "-maxgui"
        ]
        subprocess.Popen(args)
        log_message("Warte 8s, damit SAP GUI starten kann...")
        time.sleep(8)
    except FileNotFoundError:
        log_message("WARNUNG: sapshcut.exe nicht gefunden. Es wird angenommen, dass SAP bereits läuft.")
        pass


def close_excel() -> None:
    """Schließt alle laufenden Excel-Prozesse robust mit taskkill."""
    try:
        subprocess.run(["taskkill", "/F", "/IM", "excel.exe"], check=True, capture_output=True, text=True)
    except subprocess.CalledProcessError as e:
        # Excel war evtl. nicht offen – das ist ok
        _ = e


def execute_script1_llsgitls24(session, charge: str) -> None:
    """Führt Script 1 (llsgitls24.vbs) Logik in Python aus."""
    log_message("Führe Script 1 Logik aus: llsgitls24 Transaction")

    # Maximiere Fenster und öffne Transaction
    session.findById("wnd[0]").maximize()
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nLLSGITLS24"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Charge
    session.findById("wnd[0]/usr/txtS_CHARG-LOW").text = charge

    # Setze weitere Felder
    session.findById("wnd[0]/usr/ctxtS_LGNUM-LOW").text = LAGERNUMMER_VON
    session.findById("wnd[0]/usr/ctxtS_WERKS-LOW").text = WERK
    session.findById("wnd[0]/usr/ctxtS_LGORT-LOW").text = LAGERORT_VON

    # Führe aus (F8)
    session.findById("wnd[0]").sendVKey(8)

    # Warte kurz für Verarbeitung
    time.sleep(2)

    # Export zu Excel - öffne Export Dialog
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[2]").select()

    # Setze Export-Dateiname und Pfad
    session.findById("wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME").text = EXPORT_FILE_NAME
    session.findById("wnd[1]/usr/ctxtDY_PATH").text = EXPORT_FILE_PATH

    # Bestätige Export
    session.findById("wnd[1]/tbar[0]/btn[0]").press()

    log_message("Script 1 Logik abgeschlossen.")


def execute_script2_lt15(session, transportauftrag_nr: str) -> None:
    """Führt Script 2 (LT15.vbs) Logik in Python aus."""
    log_message("Führe Script 2 Logik aus: LT15 Transaction")

    # Öffne LT15 Transaction
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nLT15"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Transportauftrag Nummer
    session.findById("wnd[0]/usr/txtLTAK-TANUM").text = transportauftrag_nr

    # Führe aus (Enter)
    session.findById("wnd[0]").sendVKey(0)

    log_message("Script 2 Logik abgeschlossen.")


def execute_script3_migo(session, material_nr: str, gesamtbestand: str, charge: str) -> None:
    """Führt Script 3 (MIGO.vbs) Logik in Python aus."""
    log_message("Führe Script 3 Logik aus: MIGO Transaction")

    # Öffne MIGO Transaction
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nMIGO"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Material
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-MAKTX[1,0]").text = material_nr

    # Setze Menge
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/txtGOITEM-ERFMG[4,0]").text = gesamtbestand

    # Setze Lagerort von
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-LGOBE[6,0]").text = LAGERORT_VON

    # Setze Werk
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-NAME1[13,0]").text = WERK

    # Setze Charge
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-CHARG[10,0]").text = charge

    # Setze Lagerort nach
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMLGOBE[32,0]").text = LAGERORT_NACH

    # Setze Charge für Umlagerung
    session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]").text = charge

    # Führe aus
    session.findById("wnd[0]").sendVKey(0)

    log_message("Script 3 Logik abgeschlossen.")


def execute_script4_lt0651b(session) -> None:
    """Führt Script 4 (lt0651b.vbs) Logik in Python aus."""
    log_message("Führe Script 4 Logik aus: LT06 Transaction")

    # Öffne LT06 Transaction
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nLT06"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Lagertyp
    session.findById("wnd[0]/usr/ctxtLTAP-VLTYP").text = LAGERTYP

    # Führe aus
    session.findById("wnd[0]").sendVKey(0)

    log_message("Script 4 Logik abgeschlossen.")


def execute_script5_lt12(session) -> None:
    """Führt Script 5 (lt12.vbs) Logik in Python aus."""
    log_message("Führe Script 5 Logik aus: LT12 Transaction")

    # Öffne LT12 Transaction
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nLT12"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Checkboxen
    session.findById("wnd[0]/usr/chkRL03T-OFPOS").selected = True
    session.findById("wnd[0]/usr/chkRLIST-SUBST").selected = True
    session.findById("wnd[0]/usr/chkRLIST-SUBST").setFocus()

    # Führe aus
    session.findById("wnd[0]").sendVKey(0)
    session.findById("wnd[0]/tbar[0]/btn[11]").press()

    log_message("Script 5 Logik abgeschlossen.")


def execute_script6_lt06512(session) -> None:
    """Führt Script 6 (lt06512.vbs) Logik in Python aus."""
    log_message("Führe Script 6 Logik aus: LT06 Transaction (Lagernummer nach)")

    # Öffne LT06 Transaction
    session.findById("wnd[0]/tbar[0]/okcd").text = "/nLT06"
    session.findById("wnd[0]").sendVKey(0)

    # Setze Lagernummer nach
    session.findById("wnd[0]/usr/ctxtRL02B-LGNUM").text = LAGERNUMMER_NACH

    # Führe aus
    session.findById("wnd[0]").sendVKey(0)

    log_message("Script 6 Logik abgeschlossen.")


def find_export_file(folder: Path, base_name: str) -> Optional[Path]:
    """Sucht nach der exportierten Datei: bevorzugt .xlsx, sonst .xls."""
    xlsx = folder / f"{base_name}.xlsx"
    xls = folder / f"{base_name}.xls"
    if xlsx.exists():
        return xlsx
    if xls.exists():
        return xls
    # Fallback: irgendeine Datei, die so beginnt
    candidates = sorted(folder.glob(f"{base_name}.*"), key=lambda p: p.stat().st_mtime, reverse=True)
    return candidates[0] if candidates else None


def read_cells_from_excel(file_path: Path) -> Tuple[str, str, str]:
    """Liest U2, C2, AG2 aus Excel. Nutzt COM über 'excel.exe', da Export oft .xls ist.

    Rückgabe: (TRANSPORTAUFTRAG_NR, MATERIAL_NR, GESAMTBESTAND)
    """
    try:
        import win32com.client  # type: ignore
    except Exception as e:
        raise RuntimeError("pywin32 (win32com) wird benötigt, um Excel-Dateien auszulesen.") from e

    excel = None
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        wb = excel.Workbooks.Open(str(file_path))
        ws = wb.ActiveSheet
        # Werte als Strings lesen (None -> "")
        val_u2 = ws.Range("U2").Value
        val_c2 = ws.Range("C2").Value
        val_ag2 = ws.Range("AG2").Value
        wb.Close(SaveChanges=False)
        return str(val_u2 or "").strip(), str(val_c2 or "").strip(), str(val_ag2 or "").strip()
    finally:
        if excel is not None:
            excel.Quit()





def main() -> None:
    parser = argparse.ArgumentParser(description="Orchestriert SAP GUI Automation basierend auf beschreibung.md")
    parser.add_argument("--charge", required=True, help="Chargennummer (CHARGEN_NR)")
    parser.add_argument("--dry-run", action="store_true", help="Trockenlauf ohne SAP – simuliert alle Schritte")
    args = parser.parse_args()

    log_message(f"Orchestrator gestartet mit Charge: {args.charge}")

    if args.dry_run:
        # Trockenlauf: keine echte SAP-Interaktion; wir simulieren Variablen
        print_progress(1, 10, "Trockenlauf aktiv – keine SAP-Interaktion")
        # Simulierte Daten
        transportauftrag_nr = "1030"
        material_nr = "281603"
        gesamtbestand = "305"
        time.sleep(0.2)
        print_progress(2, 10, "Simuliere Script 1 (llsgitls24)")
        time.sleep(0.2)
        print_progress(4, 10, "Simuliere Script 2 (lt15)")
        time.sleep(0.2)
        print_progress(5, 10, "Simuliere Script 3 (MIGO)")
        time.sleep(0.2)
        print_progress(6, 10, "Simuliere Script 4 (lt0651b)")
        time.sleep(0.2)
        print_progress(7, 10, "Simuliere Script 5 (lt12)")
        time.sleep(0.2)
        print_progress(8, 10, "Simuliere Script 6 (lt06512)")
        time.sleep(0.2)
        print_progress(9, 10, "Trockenlauf-Aufräumen")
        print_progress(10, 10, "Fertig")
        return

    # 1) SAP Session holen
    print_progress(1, 10, "Verbinde mit SAP")
    log_message("Verbinde mit SAP GUI...")
    session = get_sap_session()

    # 2) Script 1: llsgitls24 -> SAP GUI Automation
    print_progress(2, 10, "Führe Script 1 (llsgitls24) aus")
    execute_script1_llsgitls24(session, args.charge)

    # Excel schließen, das SAP evtl. geöffnet hat
    log_message("Schließe Excel, um den Export zu ermöglichen...")
    close_excel()

    # Export-Datei finden und Zellen lesen
    print_progress(3, 10, "Lese Export-Excel")
    log_message(f"Suche nach Export-Datei '{EXPORT_FILE_NAME}' in '{EXPORT_FILE_PATH}'...")
    export_path = Path(EXPORT_FILE_PATH)
    export_file = find_export_file(export_path, EXPORT_FILE_NAME)
    if not export_file:
        raise FileNotFoundError(f"Export-Datei nicht gefunden in {EXPORT_FILE_PATH} mit Basis '{EXPORT_FILE_NAME}'")
    log_message(f"Export-Datei gefunden: {export_file}")
    transportauftrag_nr, material_nr, gesamtbestand = read_cells_from_excel(export_file)
    log_message(f"Gelesene Werte: TA-Nr={transportauftrag_nr}, Material={material_nr}, Bestand={gesamtbestand}")
    if not transportauftrag_nr:
        raise RuntimeError("TRANSPORTAUFTRAG_NR aus U2 ist leer.")

    # 3) Script 2: lt15 (Transportauftrag setzen)
    print_progress(4, 10, "Führe Script 2 (lt15) aus")
    execute_script2_lt15(session, transportauftrag_nr)

    # 4) Script 3: MIGO (Material, Bestand, Lagerorte, Werk, Charge)
    print_progress(5, 10, "Führe Script 3 (MIGO) aus")
    execute_script3_migo(session, material_nr, gesamtbestand, args.charge)

    # 5) Script 4: lt0651b (Lagertyp setzen)
    print_progress(6, 10, "Führe Script 4 (lt0651b) aus")
    execute_script4_lt0651b(session)

    # 6) Script 5: lt12
    print_progress(7, 10, "Führe Script 5 (lt12) aus")
    execute_script5_lt12(session)

    # 7) Script 6: lt06512 (Lagernummer nach setzen)
    print_progress(8, 10, "Führe Script 6 (lt06512) aus")
    execute_script6_lt06512(session)

    # 8) Aufräumen: Excel sicher schließen (falls wieder geöffnet) und optional SAP beenden
    print_progress(9, 10, "Räume auf (Excel/SAP)")
    close_excel()
    # SAP GUI schließen (Soft-Ansatz: Prozess killen, da scripting-close uneinheitlich)
    try:
        subprocess.run(["taskkill", "/F", "/IM", "saplogon.exe"], check=False, capture_output=True, text=True)
    except Exception:
        pass

    print_progress(10, 10, "Fertig")


if __name__ == "__main__":
    main()
