import sys
import locale
import threading
import time
import queue
import subprocess
from pathlib import Path
from typing import Optional

import tkinter as tk
from tkinter import ttk, messagebox


# ----------------------------
# Hintergrund-Worker-Struktur
# ----------------------------

class BackgroundProcess:
    """
    Kapselt die Hintergrundausführung eines externen Python-Skripts (später SAP).
    Übergibt die Charge als Argument.
    Simuliert aktuell den Prozess, bis dein echtes SAP-Skript eingebunden wird.
    """

    def __init__(self, charge: str, on_progress, on_done, on_error, on_log, script_path: Optional[Path] = None):
        self.charge = charge
        self.on_progress = on_progress
        self.on_done = on_done
        self.on_error = on_error
        self.on_log = on_log
        self.script_path = script_path
        self._thread: Optional[threading.Thread] = None
        self._cancelled = threading.Event()

    def start(self):
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def cancel(self):
        self._cancelled.set()

    def _run(self):
        """Startet den Python-Orchestrator-Prozess und parst Fortschrittzeilen.

        Erwartete Fortschrittsausgabe des Orchestrators:
        "PROGRESS <percent> <message>"
        """
        try:
            # Portable-Build: bevorzuge EXE, sonst .py
            exe_path = Path("sap_orchestrator.exe")
            extra = []
            if hasattr(self, "_extra_flag") and self._extra_flag:
                extra = [self._extra_flag]
            if exe_path.exists():
                cmd = [str(exe_path), "--charge", self.charge, *extra]
            else:
                if not self.script_path or not self.script_path.exists():
                    raise FileNotFoundError("SAP-Orchestrator wurde nicht gefunden (sap_orchestrator.py)")
                cmd = [sys.executable, str(self.script_path), "--charge", self.charge, *extra]

            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding=locale.getpreferredencoding(False),
                errors="replace",
            )

            output_lines = []
            assert proc.stdout is not None
            for raw in proc.stdout:
                output_lines.append(raw)
                line = raw.strip()
                if line.startswith("PROGRESS "):
                    try:
                        parts = line.split(" ", 2)
                        percent = int(parts[1]) if len(parts) > 1 else 0
                        message = parts[2] if len(parts) > 2 else ""
                        self.on_progress(percent, message)
                    except Exception:
                        self.on_progress(None, "")
                else:
                    self.on_log(raw)  # Jede andere Zeile ist ein Log

                if self._cancelled.is_set():
                    proc.terminate()
                    full_output = "".join(output_lines)
                    self.on_error(RuntimeError(f"Prozess abgebrochen.\n\nBisherige Ausgabe:\n{full_output}"))
                    return

            rc = proc.wait()
            if rc == 0:
                self.on_done("Prozess erfolgreich beendet")
            else:
                full_output = "".join(output_lines)
                error_lines = [l for l in output_lines if not l.strip().startswith("PROGRESS ")]
                error_details = "".join(error_lines).strip()
                raise RuntimeError(f"SAP-Orchestrator endete mit Exit-Code {rc}.\n\nDetails:\n{error_details}")
        except Exception as e:
            self.on_error(e)

    #                 self.on_error(RuntimeError("Prozess abgebrochen"))
    #                 return
    #
    #         rc = proc.wait()
    #         if rc == 0:
    #             self.on_done("Prozess erfolgreich beendet")
    #         else:
    #             raise RuntimeError(f"SAP-Skript endete mit Exit-Code {rc}")
    #     except Exception as e:
    #         self.on_error(e)


# ----------------------------
# GUI
# ----------------------------

class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Charge Prozess")
        self.geometry("420x230")
        self.resizable(False, False)

        # State
        self._worker: Optional[BackgroundProcess] = None
        self._indeterminate = False

        # Haupt-Frame
        self.main_frame = ttk.Frame(self, padding=16)
        self.main_frame.pack(fill="both", expand=True)

        # Charge Label + Entry
        self.label_charge = ttk.Label(self.main_frame, text="Charge")
        self.label_charge.grid(row=0, column=0, sticky="w", pady=(0, 8))

        self.var_charge = tk.StringVar()
        self.entry_charge = ttk.Entry(self.main_frame, textvariable=self.var_charge, width=36)
        self.entry_charge.grid(row=1, column=0, columnspan=2, sticky="we", pady=(0, 16))

        # Trockenlauf-Option
        self.var_dry_run = tk.BooleanVar(value=False)
        self.chk_dry_run = ttk.Checkbutton(self.main_frame, text="Trockenlauf (ohne SAP)", variable=self.var_dry_run)
        self.chk_dry_run.grid(row=2, column=0, columnspan=2, sticky="w", pady=(0, 8))

        # Buttons
        self.btn_start = ttk.Button(self.main_frame, text="Start", command=self.on_start)
        self.btn_start.grid(row=3, column=0, sticky="we", pady=(0, 8))

        self.btn_cancel = ttk.Button(self.main_frame, text="Abbrechen", command=self.on_cancel)
        self.btn_cancel.grid(row=3, column=1, sticky="we", pady=(0, 8))

        # Progress + Status
        self.progress = ttk.Progressbar(self.main_frame, orient="horizontal", mode="determinate", length=300)
        self.progress.grid(row=4, column=0, columnspan=2, sticky="we")

        self.var_status = tk.StringVar(value="")
        self.label_status = ttk.Label(self.main_frame, textvariable=self.var_status)
        self.label_status.grid(row=5, column=0, columnspan=2, sticky="w", pady=(8, 0))

        # Grid config
        self.main_frame.columnconfigure(0, weight=1, uniform="cols")
        self.main_frame.columnconfigure(1, weight=1, uniform="cols")

        # Tastatur-Shortcut: Enter startet
        self.bind("<Return>", lambda e: self.on_start())

    # ----------------------------
    # Ereignis-Handler
    # ----------------------------

    def on_start(self):
        charge = self.var_charge.get().strip()
        if not charge:
            messagebox.showwarning("Eingabe fehlt", "Bitte eine Charge eingeben.")
            return

        # UI sperren und vorbereiten
        self._set_running_state(True)
        self._set_progress_indeterminate(False)
        self.progress["value"] = 0
        self.var_status.set("Prozess wird gestartet...")

        # Worker starten (aktuell Simulation)
        # Kommando-Argumente
        dry_flag = "--dry-run" if self.var_dry_run.get() else None

        self._worker = BackgroundProcess(
            charge=charge,
            on_progress=self._on_worker_progress,
            on_done=self._on_worker_done,
            on_error=self._on_worker_error,
            script_path=Path("sap_orchestrator.py")
        )
        # Hack: übergebe Dry-Run-Flag über Instanzvariable
        self._worker._extra_flag = dry_flag
        self._worker.start()

    def on_cancel(self):
        if self._worker:
            self._worker.cancel()
            self.var_status.set("Abbruch angefordert...")

    # ----------------------------
    # Callbacks vom Worker
    # ----------------------------

    def _on_worker_progress(self, percent: Optional[int]):
        # Im Simulationsmodus haben wir konkrete Prozentwerte (0-100).
        # Im echten Modus könntest du None übergeben, um indeterminate anzuzeigen.
        if percent is None:
            # Unbestimmter Fortschritt
            if not self._indeterminate:
                self._set_progress_indeterminate(True)
        else:
            if self._indeterminate:
                self._set_progress_indeterminate(False)
            self.progress["value"] = percent
            self.var_status.set(f"Fortschritt: {percent}%")

    def _on_worker_done(self, message: str):
        self.progress["value"] = 100
        self.var_status.set(message)
        # Kurze Verzögerung, dann UI zurücksetzen
        self.after(800, self._reset_ui_after_finish)

    def _on_worker_error(self, error: Exception):
        self._set_running_state(False)
        self._set_progress_indeterminate(False)
        self.progress["value"] = 0
        self.var_status.set("")
        messagebox.showerror("Fehler", str(error))

    # ----------------------------
    # UI-Utils
    # ----------------------------

    def _set_running_state(self, running: bool):
        self.entry_charge.configure(state="disabled" if running else "normal")
        self.btn_start.configure(state="disabled" if running else "normal")
        self.btn_cancel.configure(state="normal" if running else "disabled")

    def _set_progress_indeterminate(self, enable: bool):
        self._indeterminate = enable
        if enable:
            self.progress.configure(mode="indeterminate")
            self.progress.start(10)
        else:
            self.progress.stop()
            self.progress.configure(mode="determinate")

    def _reset_ui_after_finish(self):
        # Status meldet Erfolg, dann auf "Haupt-GUI" zurück
        self._set_running_state(False)
        self._set_progress_indeterminate(False)
        self.progress["value"] = 0
        self.var_status.set("Prozess erfolgreich beendet")
        # Optional nach kurzer Anzeige wieder leeren
        self.after(1200, lambda: self.var_status.set(""))

        # Worker freigeben
        self._worker = None


def main():
    app = App()
    app.mainloop()


if __name__ == "__main__":
    main()