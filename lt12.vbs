If Not IsObject(application) Then
   Set SapGuiAuto  = GetObject("SAPGUI")
   Set application = SapGuiAuto.GetScriptingEngine
End If
If Not IsObject(connection) Then
   Set connection = application.Children(0)
End If
If Not IsObject(session) Then
   Set session    = connection.Children(0)
End If
If IsObject(WScript) Then
   WScript.ConnectObject session,     "on"
   WScript.ConnectObject application, "on"
End If
session.findById("wnd[0]").maximize
session.findById("wnd[0]/tbar[0]/okcd").text = "/nLT12"
session.findById("wnd[0]").sendVKey 0
session.findById("wnd[0]/usr/chkRL03T-OFPOS").selected = true
session.findById("wnd[0]/usr/chkRLIST-SUBST").selected = true
session.findById("wnd[0]/usr/chkRLIST-SUBST").setFocus
session.findById("wnd[0]").sendVKey 0
session.findById("wnd[0]/tbar[0]/btn[11]").press
