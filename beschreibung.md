In dieser datei ist eine UI @app.py in der man eine Chargennummer eingeben soll, wenn man das gemacht hat und start drückt, möchte ich das du mit ein weiteres python script schreibst das folgendes macht.
Das python script soll erstmal prüfen ob eine SAP session aktiv ist, wenn ja dann nutzt es die aktive session und öffnet ein neues SAP GUI, wenn nicht dann soll es sap im sso starten mit diesen variablen:

<SAP>
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe";
SAP_SYSTEM_ID = "TS4";
SAP_CLIENT = "009";
SAP_LANGUAGE = "DE";
</SAP>

Desweiteren Benötigt das script folgende Variablen:
<Variablen>
LAGERORT_VON = 1090;
LAGERORT_NACH = 1010;
LAGERTYP = 957;
LAGERNUMMER_VON = 51B;
LAGERNUMMER_NACH = 512;
WERK = 5100;
CHARGEN_NR = Die übergebene Chargennummer aus der UI;
EXPORT_FILE_NAME = lsgitls24;
EXPORT_FILE_PATH = C:\\TEMP;
</Variablen>

dann soll es folgende vbs Scripte ausführen in dieser reihenfolge:
## Script 1:
dieses Script @llsgitls24.vbs 
- In die folgenden felder eintragen:
	- (session.findById("wnd[0]/usr/txtS_CHARG-LOW").text = "6356375708") soll es die gespeicherte Variable "CHARGEN_NR" eintragen.
 - Beim Speichern der Excel folgendes eintragen:
	- (session.findById("wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME").text = "lsgitls24") soll es die gespeicherte Variable "EXPORT_FILE_NAME" eintragen.
	- (session.findById("wnd[1]/usr/ctxtDY_PATH").text = "C:\\TEMP") soll es die gespeicherte Variable "EXPORT_FILE_PATH" eintragen.
  - Am ende vom skript wird eine Excel Datei gespeichert und diese wird von SAP auch gleich geöffnet, es soll erstmal die excel schließen wie folgt:
```Python
def close_excel():
    """Schließt alle laufenden Excel-Prozesse robust mit taskkill."""
    logger.info("Versuche, alle Excel-Prozesse zu schließen...")
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], check=True, capture_output=True, text=True)
        logger.info("Excel wurde erfolgreich geschlossen.")
    except subprocess.CalledProcessError as e:
        if e.stderr and ("not found" in e.stderr or "Der Prozess" in e.stderr):
            logger.info("Excel war nicht geöffnet, nichts zu tun.")
        else:
            logger.error(f"Fehler beim Schließen von Excel: {e.stderr}")
    except FileNotFoundError:
        logger.error("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")
```
  - dann soll es aus der gespeicherten excel aus dem Feldern; 
	  - "U2" den Inhalt Kopieren und als variable "TRANSPORTAUFTRAG_NR" speichern
	  - "C2" den Inhalt Kopieren und als variable "MATERIAL_NR" speichern
	  - "AG2" den Inhalt Kopieren und als variable "GESAMTBESTAND" speichern.
  - Wenn das erledigt ist Direkt weiter mit Script 2

## Script 2:
dieses Script @LT15.vbs 

- In die folgenden felder eintragen:
	- (session.findById("wnd[0]/usr/txtLTAK-TANUM").text = "1030") soll es die gespeicherte Variable "TRANSPORTAUFTRAG_NR" eintragen
- Wenn das script fertig ist Direkt weiter mit Script 3

## Script 3:
dieses Script @MIGO.vbs 

- In die folgenden felder eintragen:
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-MAKTX[1,0]").text = "281603") soll es die gespeicherte Variable "MATERIAL_NR" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/txtGOITEM-ERFMG[4,0]").text = "305") soll es die gespeicherte Variable "GESAMTBESTAND" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-LGOBE[6,0]").text = "1090") soll es die gespeicherte Variable "LAGERORT_VON" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-NAME1[13,0]").text = "5100") soll es die gespeicherte Variable "WERK" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-CHARG[10,0]").text = "6356360869") soll es die gespeicherte Variable "CHARGEN_NR" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMLGOBE[32,0]").text = "1010") soll es die gespeicherte Variable "LAGERORT_NACH" eintragen.
	- (session.findById("wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]").text = "6356360869") soll es die gespeicherte Variable "CHARGEN_NR" eintragen.
- Wenn das script fertig ist Direkt weiter mit Script 4

## Script 4:
dieses Script @lt0651b.vbs 

- In die folgenden felder eintragen:
	- (session.findById("wnd[0]/usr/ctxtLTAP-VLTYP").text = "957") soll es die gespeicherte Variable "LAGERTYP" eintragen.
	- (session.findById("wnd[0]/usr/ctxtRL02B-LGNUM").text = "512") soll es die gespeicherte Variable "LAGERNUMMER_VON" eintragen.
- Wenn das script fertig ist Direkt weiter mit Script 5

## Script 5:
dieses Script @lt12b.vbs 

- Wenn das script fertig ist Direkt weiter mit Script 6

## Script 6:
dieses Script @lt06512.vbs 

- In die folgenden felder eintragen:
	- (session.findById("wnd[0]/usr/ctxtRL02B-LGNUM").text = "512") soll es die gespeicherte Variable "LAGERNUMMER_NACH = 512" eintragen.
- Ende

Nachdem alle SAP Scripte durchgelaufen sind soll es das Aktuelle SAP GUI Schließen, die gespeicherte Excel